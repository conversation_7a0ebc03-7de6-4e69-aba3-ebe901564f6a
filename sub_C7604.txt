int __fastcall sub_C7604(int *a1, unsigned __int8 *a2, int a3, int a4, int a5, unsigned __int8 *a6)
{
  int v10; // r4
  int v11; // r8
  int i; // r4
  int v13; // r0
  int v14; // r0
  int v15; // r10
  char *v16; // r6
  int v17; // r9
  char *v18; // r3
  int v19; // r3
  int v20; // r5
  double v21; // d16
  int v22; // r4
  unsigned int v23; // r0
  unsigned int v24; // r4
  int v25; // r10
  char *v26; // r5
  int v27; // r4
  char *v28; // r3
  int v29; // r4
  int v30; // r0
  int v31; // r0
  int v32; // r1
  double v33; // d16
  int v34; // r4
  double v35; // d16
  char v36; // r4
  int v37; // r5
  int v38; // r4
  double v39; // d16
  int v40; // r0
  int v41; // r5
  int v42; // r0
  int v43; // r3
  int v44; // r2
  int v45; // r10
  int v46; // r9
  int v47; // r0
  int j; // r4
  int v49; // r5
  int v50; // r0
  int v51; // r6
  int v52; // r5
  int v53; // r4
  int v54; // r11
  int v55; // r8
  int v56; // r5
  bool v57; // zf
  _BOOL4 v58; // r0
  int *v59; // r2
  const char *v60; // r4
  size_t v61; // r0
  _DWORD *v62; // r4
  int *v63; // r5
  int v64; // r9
  void *v65; // r8
  int v66; // r0
  int v67; // r4
  int v68; // r4
  int v69; // r0
  unsigned __int8 *v70; // r10
  void *v71; // r0
  int v72; // r0
  int v73; // r0
  int v74; // r9
  char *v75; // r6
  int v76; // r4
  char *v77; // r3
  int v78; // r4
  int v79; // r2
  int v80; // r0
  int v81; // r4
  int v82; // r4
  int v83; // r8
  char *v84; // r1
  int v85; // r9
  double v86; // d16
  char *v87; // r1
  int v88; // r6
  int v89; // r4
  int v90; // r0
  int v91; // r0
  double v92; // d16
  int v93; // r1
  int v94; // r2
  int v95; // r0
  int v96; // r9
  char *v97; // r5
  int v98; // r4
  char *v99; // r3
  int v100; // r4
  char *v101; // r1
  int v102; // r5
  int v103; // r0
  int v104; // r0
  char *v105; // r1
  int v106; // r6
  int v107; // r0
  int v108; // r0
  int v110; // [sp+10h] [bp-200h]
  int v111; // [sp+14h] [bp-1FCh]
  int v112; // [sp+18h] [bp-1F8h]
  int v113; // [sp+1Ch] [bp-1F4h]
  int v114; // [sp+20h] [bp-1F0h]
  int v115; // [sp+24h] [bp-1ECh]
  int v116; // [sp+28h] [bp-1E8h]
  unsigned __int8 *v117; // [sp+30h] [bp-1E0h]
  int v118; // [sp+34h] [bp-1DCh]
  int v119; // [sp+38h] [bp-1D8h]
  int v120; // [sp+3Ch] [bp-1D4h]
  int *v121; // [sp+40h] [bp-1D0h]
  _BYTE v122[8]; // [sp+44h] [bp-1CCh] BYREF
  void *v123; // [sp+4Ch] [bp-1C4h]
  double v124; // [sp+50h] [bp-1C0h] BYREF
  void *v125; // [sp+58h] [bp-1B8h]
  _BYTE v126[8]; // [sp+5Ch] [bp-1B4h] BYREF
  void *v127; // [sp+64h] [bp-1ACh]
  double v128; // [sp+68h] [bp-1A8h] BYREF
  void *v129; // [sp+70h] [bp-1A0h]
  _BYTE v130[8]; // [sp+74h] [bp-19Ch] BYREF
  void *v131; // [sp+7Ch] [bp-194h]
  double v132; // [sp+80h] [bp-190h] BYREF
  void *v133; // [sp+88h] [bp-188h]
  _BYTE v134[8]; // [sp+8Ch] [bp-184h] BYREF
  void *v135; // [sp+94h] [bp-17Ch]
  double v136; // [sp+98h] [bp-178h] BYREF
  void *v137; // [sp+A0h] [bp-170h]
  double v138; // [sp+A8h] [bp-168h] BYREF
  void *v139; // [sp+B0h] [bp-160h]
  double v140; // [sp+B8h] [bp-158h] BYREF
  void *v141; // [sp+C0h] [bp-150h]
  double v142; // [sp+C8h] [bp-148h] BYREF
  void *v143; // [sp+D0h] [bp-140h]
  _BYTE v144[8]; // [sp+DCh] [bp-134h] BYREF
  void *v145; // [sp+E4h] [bp-12Ch]
  _DWORD v146[2]; // [sp+E8h] [bp-128h] BYREF
  void *v147; // [sp+F0h] [bp-120h]
  _BYTE v148[8]; // [sp+F4h] [bp-11Ch] BYREF
  void *v149; // [sp+FCh] [bp-114h]
  double v150; // [sp+100h] [bp-110h] BYREF
  void *v151; // [sp+108h] [bp-108h]
  double v152; // [sp+110h] [bp-100h] BYREF
  void *v153; // [sp+118h] [bp-F8h]
  _BYTE v154[8]; // [sp+120h] [bp-F0h] BYREF
  void *v155; // [sp+128h] [bp-E8h]
  _BYTE v156[8]; // [sp+12Ch] [bp-E4h] BYREF
  void *v157; // [sp+134h] [bp-DCh]
  double v158; // [sp+138h] [bp-D8h] BYREF
  void *v159; // [sp+140h] [bp-D0h]
  int v160; // [sp+148h] [bp-C8h] BYREF
  int v161; // [sp+14Ch] [bp-C4h] BYREF
  int v162; // [sp+150h] [bp-C0h] BYREF
  int v163; // [sp+154h] [bp-BCh] BYREF
  double v164; // [sp+158h] [bp-B8h] BYREF
  void *v165; // [sp+160h] [bp-B0h]
  _BYTE v166[16]; // [sp+168h] [bp-A8h] BYREF
  double v167; // [sp+178h] [bp-98h] BYREF
  void *v168; // [sp+180h] [bp-90h]
  double v169; // [sp+188h] [bp-88h] BYREF
  void *v170; // [sp+190h] [bp-80h]
  double v171; // [sp+198h] [bp-78h] BYREF
  void *v172; // [sp+1A0h] [bp-70h]
  int v173; // [sp+1A4h] [bp-6Ch] BYREF
  int v174; // [sp+1A8h] [bp-68h] BYREF
  int v175; // [sp+1ACh] [bp-64h] BYREF
  double v176; // [sp+1B0h] [bp-60h] BYREF
  void *v177; // [sp+1B8h] [bp-58h]
  _BYTE v178[16]; // [sp+1C0h] [bp-50h] BYREF
  int v179; // [sp+1D0h] [bp-40h]
  __int64 v180; // [sp+1D4h] [bp-3Ch] BYREF
  void *v181; // [sp+1DCh] [bp-34h]
  _DWORD v182[4]; // [sp+1E0h] [bp-30h] BYREF

  v10 = 0;
  if ( !j_Java_com_dianjiqi_OoOooO00ooo0O0o_OOOo000O00O0oo0(a1) )
    return v10;
  sub_169FC4(v178, &unk_611598);
  v181 = 0;
  v180 = 0LL;
  v179 = dword_6115A8;
  if ( (unsigned __int8)byte_6115AC << 31 )
  {
    sub_EEE78(&v180, dword_6115B4, dword_6115B0);
  }
  else
  {
    v181 = (void *)dword_6115B4;
    v180 = *(_QWORD *)&byte_6115AC;
  }
  sub_EEB94(v182, &unk_6115B8);
  v117 = a2;
  v118 = a3;
  v121 = a1;
  v119 = sub_BF718(a1);
  v11 = sub_BF95C(a1);
  for ( i = sub_F4A18(&unk_611598); i > 0; sub_F9E40(&unk_611598, i, v14) )
  {
    sub_C5100(a1, v11, --i);
    v13 = sub_F56F4(&unk_611598, i, -1);
    v14 = sub_1109BC(v13);
  }
  v15 = (*(int (__fastcall **)(int *, int))(*a1 + 124))(a1, v119);
  v116 = a4;
  sub_F310C(&v142, 1);
  v16 = (char *)v143;
  v17 = LOBYTE(v142);
  sub_F415C(&v140);
  v18 = (char *)v141;
  if ( !(v17 << 31) )
    v16 = (char *)&v142 + 1;
  if ( (LOBYTE(v140) & 1) == 0 )
    v18 = (char *)&v140 + 1;
  v20 = (*(int (__fastcall **)(int *, int, char *, char *))(*v121 + 132))(v121, v15, v16, v18);
  if ( LOBYTE(v140) << 31 )
    operator delete(v141);
  if ( LOBYTE(v142) << 31 )
    operator delete(v143);
  sub_C1578(v121, v119, v20, v19);
  (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v15);
  sub_C59BC(v121);
  if ( a5 == 3 )
  {
    sub_10D500(&v142, v178, v117);
    if ( *a6 << 31 )
    {
      **((_BYTE **)a6 + 2) = 0;
      *((_DWORD *)a6 + 1) = 0;
    }
    else
    {
      *(_WORD *)a6 = 0;
    }
    sub_EF064(a6, 0);
    *(double *)a6 = v142;
    v23 = *a6;
    v24 = *((_DWORD *)a6 + 1);
    *((_DWORD *)a6 + 2) = v143;
    if ( (v23 & 1) == 0 )
      v24 = v23 >> 1;
    v22 = v24 != 0;
  }
  else
  {
    v171 = 0.0;
    v172 = 0;
    if ( *v117 << 31 )
    {
      sub_EEE78(&v171, *((_DWORD *)v117 + 2), *((_DWORD *)v117 + 1));
    }
    else
    {
      v21 = *(double *)v117;
      v172 = (void *)*((_DWORD *)v117 + 2);
      v171 = v21;
    }
    v22 = sub_10C570(v178, &v171, 0);
    v23 = LOBYTE(v171) << 31;
    if ( v23 )
      operator delete(v172);
  }
  if ( v22 == 1 )
  {
    v23 = sub_F6F88(v178);
    if ( v23 == 1 )
    {
      v23 = sub_13D708();
      if ( !v23 )
      {
        v37 = sub_BF718(v121);
        v38 = sub_BF7EC(v121, v37);
        (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v37);
        v169 = 0.0;
        v170 = 0;
        if ( *v117 << 31 )
        {
          sub_EEE78(&v169, *((_DWORD *)v117 + 2), *((_DWORD *)v117 + 1));
        }
        else
        {
          v39 = *(double *)v117;
          v170 = (void *)*((_DWORD *)v117 + 2);
          v169 = v39;
        }
        sub_C6084(v121, v38, &v169, v118, v116, -1, a5);
        if ( LOBYTE(v169) << 31 )
          operator delete(v170);
        (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v38);
        (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v11);
        (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v119);
        v72 = 2;
        goto LABEL_205;
      }
    }
  }
  v25 = sub_EF6AC(v23);
  v115 = v22;
  sub_F335E(&v142);
  v26 = (char *)v143;
  v27 = LOBYTE(v142);
  sub_F42DC(&v140, 0);
  v28 = (char *)v141;
  if ( !(v27 << 31) )
    v26 = (char *)&v142 + 1;
  if ( (LOBYTE(v140) & 1) == 0 )
    v28 = (char *)&v140 + 1;
  v29 = (*(int (__fastcall **)(int *, int, char *, char *))(*v121 + 452))(v121, v25, v26, v28);
  if ( LOBYTE(v140) << 31 )
    operator delete(v141);
  v30 = LOBYTE(v142) << 31;
  if ( v30 )
    operator delete(v143);
  v31 = sub_EF6AC(v30);
  sub_BEAEC(v121, v31, v29, 1);
  j_Java_com_dianjiqi_OoOooO00ooo0O0o_Ooo00oO0O0O0oO(v121, v32, 0, -v118);
  sub_C58CC(v121);
  if ( v116 )
    goto LABEL_49;
  v167 = 0.0;
  v168 = 0;
  if ( *v117 << 31 )
  {
    sub_EEE78(&v167, *((_DWORD *)v117 + 2), *((_DWORD *)v117 + 1));
  }
  else
  {
    v33 = *(double *)v117;
    v168 = (void *)*((_DWORD *)v117 + 2);
    v167 = v33;
  }
  v34 = sub_1134B8(&unk_6115D0, &v167);
  if ( LOBYTE(v167) << 31 )
    operator delete(v168);
  if ( !v34 )
  {
LABEL_49:
    sub_16A144(&unk_611598, v178);
    dword_6115A8 = v179;
    sub_C6470(&byte_6115AC, &v180);
    sub_EED30(&unk_6115B8, v182[0], v182[1]);
    v36 = 1;
  }
  else
  {
    v164 = 0.0;
    v165 = 0;
    if ( *v117 << 31 )
    {
      sub_EEE78(&v164, *((_DWORD *)v117 + 2), *((_DWORD *)v117 + 1));
    }
    else
    {
      v35 = *(double *)v117;
      v165 = (void *)*((_DWORD *)v117 + 2);
      v164 = v35;
    }
    sub_115B04(v166, &unk_6115D0, &v164);
    sub_F4898(&unk_611598, v166);
    sub_16A0CC(v166);
    if ( LOBYTE(v164) << 31 )
      operator delete(v165);
    v36 = 0;
  }
  v40 = sub_F6B64(&unk_611598);
  sub_F6B54(&unk_611598, v40);
  sub_C57D0(v121, dword_611630);
  v41 = sub_C247C(61, 1);
  if ( v118 == 1 )
  {
    v173 = -1;
    v42 = sub_BF3B4(v121, &v173);
    (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v42);
    v43 = v41;
    if ( v41 )
      v43 = 1;
    sub_C40D4(v121, v173, 0, v43);
  }
  v44 = sub_F4A18(&unk_611598);
  v120 = 0;
  v111 = v41;
  if ( v44 < 1 )
  {
    v45 = 0;
  }
  else
  {
    v114 = (v41 != 0) & (unsigned __int8)v118;
    v45 = 0;
    v46 = 0;
    v47 = ((unsigned int)(a5 - 1) < 2) & (unsigned __int8)v36;
    v112 = v47;
    v113 = v44;
    do
    {
      if ( v47 == 1 )
      {
        if ( sub_F6E1C(&unk_611598, v46) == 1 )
        {
          for ( j = 0; j < 2; ++j )
          {
            v163 = sub_FAF88(&unk_611598, v46, j);
            v162 = sub_FB1C0(&unk_611598, v46, j);
            v49 = sub_B9BC0(&v163, &v162);
            if ( v49 <= -1 )
              sub_100FB8(&unk_611598, v46, 0, j, v163, v162);
            v120 |= v49 < 0;
            v45 |= v49 == 0;
          }
        }
        else
        {
          v50 = sub_F6BCC(&unk_611598, v46);
          if ( v50 >= 1 )
          {
            v51 = 0;
            v110 = v50;
            do
            {
              v52 = sub_F6C00(&unk_611598, v46, v51);
              if ( v52 >= 1 )
              {
                v53 = 0;
                do
                {
                  v161 = sub_FB3F8(&unk_611598, v46, v51, v53);
                  v54 = v11;
                  v55 = v52;
                  v160 = sub_FB43C(&unk_611598, v46, v51, v53);
                  v56 = sub_B9BC0(&v161, &v160);
                  if ( (v56 & 0xFFFFFFFD) == 0xFFFFFFFD )
                    sub_100AB0(&unk_611598, v46, v51, v53, v161);
                  if ( (unsigned int)(v56 + 3) <= 1 )
                    sub_100B3C(&unk_611598, v46, v51, v53, v160);
                  v57 = v56 == 0;
                  v58 = v56 < 0;
                  ++v53;
                  v52 = v55;
                  v120 |= v58;
                  v45 |= v57;
                  v11 = v54;
                }
                while ( v53 < v52 );
              }
              ++v51;
            }
            while ( v51 < v110 );
          }
        }
      }
      sub_100DA8(&unk_611598, v46, v114);
      sub_C493C(v121, v11, v119, v46++, 1, 2, 1, v114);
      v47 = v112;
    }
    while ( v46 < v113 );
  }
  (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v11);
  (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v119);
  if ( v115 == 1 )
  {
    if ( (v120 | v45) << 31 )
    {
      v59 = dword_C8814;
      v140 = 0.0;
      v141 = 0;
      if ( (v45 & 1) == 0 )
        v59 = dword_C8800;
      if ( (v120 & 1) == 0 )
        v59 = dword_C8800;
      v60 = (const char *)dword_C8830;
      if ( (v120 & 1) == 0 )
        v60 = (const char *)v59;
      if ( (v45 & 1) != 0 )
        v60 = (const char *)v59;
      v61 = strlen(v60);
      sub_EEE78(&v140, v60, v61);
      sub_C0320((int)&v142, (char *)dword_C8848);
      v62 = (_DWORD *)sub_EF260(&v142, &unk_57DF57, 75);
      v63 = v121;
      v64 = *(unsigned __int8 *)v62;
      qmemcpy(&v176, (char *)v62 + 1, 7u);
      v65 = (void *)v62[2];
      *v62 = 0;
      v62[1] = 0;
      v62[2] = 0;
      if ( LOBYTE(v140) << 31 )
      {
        *(_BYTE *)v141 = 0;
        HIDWORD(v140) = 0;
      }
      else
      {
        LOWORD(v140) = 0;
      }
      v70 = v117;
      sub_EF064(&v140, 0);
      LOBYTE(v140) = v64;
      qmemcpy((char *)&v140 + 1, &v176, 7u);
      LODWORD(v176) = 0;
      BYTE6(v176) = 0;
      v141 = v65;
      WORD2(v176) = 0;
      if ( LOBYTE(v142) << 31 )
      {
        operator delete(v143);
        v64 = LOBYTE(v140);
      }
      v158 = 0.0;
      v159 = 0;
      if ( v64 << 31 )
      {
        sub_EEE78(&v158, v141, HIDWORD(v140));
      }
      else
      {
        v159 = v141;
        v158 = v140;
      }
      v79 = v118;
      if ( v118 )
        v79 = 2;
      sub_BEB3C(v121, &v158, v79);
      if ( LOBYTE(v158) << 31 )
        operator delete(v159);
      if ( !(LOBYTE(v140) << 31) )
        goto LABEL_139;
      v71 = v141;
    }
    else
    {
      v63 = v121;
      v70 = v117;
      if ( v111 )
        goto LABEL_139;
      if ( !v118 )
        goto LABEL_139;
      sub_C0320((int)v156, (char *)dword_C87EC);
      v63 = v121;
      sub_BEB3C(v121, v156, 2000);
      v70 = v117;
      if ( !(v156[0] << 31) )
        goto LABEL_139;
      v71 = v157;
    }
    operator delete(v71);
LABEL_139:
    if ( v118 == 1 )
    {
      v174 = -1;
      v80 = sub_BF3B4(v63, &v174);
      (*(void (__fastcall **)(int *, int))(*v63 + 92))(v63, v80);
      v81 = v174;
    }
    else
    {
      v82 = sub_BF718(v63);
      v83 = sub_BF7EC(v63, v82);
      (*(void (__fastcall **)(int *, int))(*v63 + 92))(v63, v82);
      sub_11097C(v154);
      sub_F6964(&v142, &unk_611598, v154);
      v84 = (char *)v143;
      if ( (LOBYTE(v142) & 1) == 0 )
        v84 = (char *)&v142 + 1;
      v85 = (*(int (__fastcall **)(int *, char *))(*v63 + 668))(v63, v84);
      if ( LOBYTE(v142) << 31 )
        operator delete(v143);
      if ( v154[0] << 31 )
        operator delete(v155);
      v152 = 0.0;
      v153 = 0;
      if ( *v70 << 31 )
      {
        sub_EEE78(&v152, *((_DWORD *)v70 + 2), *((_DWORD *)v70 + 1));
      }
      else
      {
        v86 = *(double *)v70;
        v153 = (void *)*((_DWORD *)v70 + 2);
        v152 = v86;
      }
      sub_F6A10(&v142, &unk_611598, &v152);
      v87 = (char *)v143;
      if ( (LOBYTE(v142) & 1) == 0 )
        v87 = (char *)&v142 + 1;
      v88 = (*(int (__fastcall **)(int *, char *))(*v63 + 668))(v63, v87);
      if ( LOBYTE(v142) << 31 )
        operator delete(v143);
      if ( LOBYTE(v152) << 31 )
        operator delete(v153);
      v89 = sub_F685C(&unk_611598, -1);
      v90 = sub_14AC24(10, 100);
      v91 = sub_F6884(&unk_611598, -v90);
      v81 = sub_1124B0(&unk_6115D0, v63, v83, v89, v91, v85, v88, 0);
      (*(void (__fastcall **)(int *, int))(*v63 + 92))(v63, v88);
      (*(void (__fastcall **)(int *, int))(*v63 + 92))(v63, v85);
      (*(void (__fastcall **)(int *, int))(*v63 + 92))(v63, v83);
      sub_C40D4(v63, v81, 0, 0);
    }
    v150 = 0.0;
    v151 = 0;
    if ( *v70 << 31 )
    {
      sub_EEE78(&v150, *((_DWORD *)v70 + 2), *((_DWORD *)v70 + 1));
    }
    else
    {
      v92 = *(double *)v70;
      v151 = (void *)*((_DWORD *)v70 + 2);
      v150 = v92;
    }
    sub_F488E(v148, &unk_611598);
    v93 = 1;
    if ( v116 )
      v93 = -1;
    if ( a5 == 3 )
      v93 = 2;
    sub_113680(&unk_6115D0, v81, &v150, v148, v93);
    if ( v148[0] << 31 )
      operator delete(v149);
    if ( LOBYTE(v150) << 31 )
      operator delete(v151);
    v94 = sub_11349C(&unk_6115D0);
    if ( v81 != v94 )
      sub_C3FC0(v63, v81, v94);
    v72 = 1;
    goto LABEL_205;
  }
  if ( v118 == 1 )
  {
    v175 = -1;
    v66 = sub_BF3B4(v121, &v175);
    (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v66);
    v67 = v175;
    v146[0] = 0;
    v146[1] = 0;
    v147 = 0;
    sub_EEE78(v146, &unk_57D240, 0);
    sub_169EB8(&v142, 0);
    sub_16B404(v144, &v142);
    sub_113680(&unk_6115D0, v67, v146, v144, 0);
    if ( v144[0] << 31 )
      operator delete(v145);
    sub_16A0CC(&v142);
    if ( LOBYTE(v146[0]) << 31 )
      operator delete(v147);
  }
  v68 = sub_11293C(&unk_6115D0);
  v69 = sub_11349C(&unk_6115D0);
  sub_C3FC0(v121, v68, v69);
  sub_113494(&unk_6115D0, v68);
  if ( v115 == -2 )
  {
    v138 = 0.0;
    v139 = 0;
    v73 = sub_EEE78(
            &v138,
            "93bcRyt4K-RqujNG-oOp7owvp3fEAwM3ynsMeO2sY0RTlLRZAaZHpUcRB5mk21Zn1BL2AcYtRhRcxamU4Lc9gxuzqwhL9SVeUCigAIgbwZq_"
            "PmqA6bcfYf8I5AzN3HXNtj6QMtOtcmPTU8e2N-ppdGa6P1XYhkVGSHwed0R0lkC7GoJg2n27uIHHA_9V0qHolBPhgVEtSMxBWOdxJdzRdE5Ni00SxC6o",
            224);
    v74 = sub_EF6AC(v73);
    sub_F384A(&v142, 0);
    v75 = (char *)v143;
    v76 = LOBYTE(v142);
    sub_F43D4(&v140, 0);
    v77 = (char *)v141;
    if ( !(v76 << 31) )
      v75 = (char *)&v142 + 1;
    if ( (LOBYTE(v140) & 1) == 0 )
      v77 = (char *)&v140 + 1;
    v78 = (*(int (__fastcall **)(int *, int, char *, char *))(*v121 + 452))(v121, v74, v75, v77);
    if ( LOBYTE(v140) << 31 )
      operator delete(v141);
    if ( LOBYTE(v142) << 31 )
      operator delete(v143);
    v132 = 0.0;
    v133 = 0;
    if ( LOBYTE(v138) << 31 )
    {
      sub_EEE78(&v132, v139, HIDWORD(v138));
    }
    else
    {
      v133 = v139;
      v132 = v138;
    }
    sub_14D834(v130);
    sub_14D9A0(&v142, &v132, 1, v130);
    v105 = (char *)v143;
    if ( (LOBYTE(v142) & 1) == 0 )
      v105 = (char *)&v142 + 1;
    v106 = (*(int (__fastcall **)(int *, char *))(*v121 + 668))(v121, v105);
    if ( LOBYTE(v142) << 31 )
      operator delete(v143);
    if ( v130[0] << 31 )
      operator delete(v131);
    v107 = LOBYTE(v132) << 31;
    if ( v107 )
      operator delete(v133);
    v108 = sub_EF6AC(v107);
    sub_BEAEC(v121, v108, v78, v106);
    (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v106);
    if ( LOBYTE(v138) << 31 )
      operator delete(v139);
    v72 = -2;
  }
  else
  {
    if ( v115 != -1 )
    {
      if ( (unsigned int)(v115 + 5) <= 2 )
      {
        v128 = 0.0;
        v129 = 0;
        sub_EEE78(&v128, "ca53r72r1VkvUAgT-M5hqFdt_5yn3vh7pGFteO1QVwr6tH9fKtmNooB9oTuTvv7xrg4H3qRpdcPL0YhhEZA", 83);
        switch ( v115 )
        {
          case -5:
            sub_EEF24(
              &v128,
              "fc06Ud-2BZiej9EXs-qN34uGXYQbXrKsIJli0psK9CZ--r-_KtaTOBqe3OUijzID0OSAeEvst8anJg5SB52cejEYSORmiDHnZ2AnPTN7v8"
              "FrYVdH5bLazmUC2Sncms_SAZkU_2QFnl3JWNpxkNfM4A",
              150);
            break;
          case -4:
            sub_EEF24(
              &v128,
              "59dbRs7sf8-AJUia1uF2xNaqbw9rBkmErddEUcb9Y-ldDup8JCPX_8rpBkq2eHIjaJzlBgTuAJ7FaypW9zvGSNTFmnJs124fCyFASJ2Emj"
              "wRTHWi7mTBPpAs9Tjo300dkX33HkaOI5IM4zbGciYoQlMGv2DH5QAK4CbTF8j-S3hvh-XmKIQ-4iNJMwx65pHfKsBsnu1gvP1RRKyFhmeMXg",
              214);
            break;
          case -3:
            sub_EEF24(
              &v128,
              "eb6dlVYQMOYK60MlOQxwdthamPn1unh5DmQUmyPgcMQhSoji6dbD9voCnU0cVw4aATidBgPFVzB9sDUV96IPDPd17zSlA7XA5MyFmnryfw"
              "_K-YOHrRzXjKD4JPbqn45jLZrrbYvcSa72LZZTxl5vZQ",
              150);
            break;
        }
        v124 = 0.0;
        v125 = 0;
        if ( LOBYTE(v128) << 31 )
        {
          sub_EEE78(&v124, v129, HIDWORD(v128));
        }
        else
        {
          v125 = v129;
          v124 = v128;
        }
        sub_14D834(v122);
        sub_14D9A0(v126, &v124, 1, v122);
        sub_BEB3C(v121, v126, 2);
        if ( v126[0] << 31 )
          operator delete(v127);
        if ( v122[0] << 31 )
          operator delete(v123);
        if ( LOBYTE(v124) << 31 )
          operator delete(v125);
        if ( LOBYTE(v128) << 31 )
          operator delete(v129);
      }
      goto LABEL_206;
    }
    sub_169EB8(&v142, 0);
    v176 = 0.0;
    v177 = 0;
    sub_EEE78(
      &v176,
      "e40bjWQjoAF5Be0LKNg_O2qHobSxUld2EV2ATps3k_PAQF3sbWI-kIyEeA1g_PSxHq8VKP4r9YWMAdNS0h2vv3gKWGWZfQ9bmUdOvh0H21iRrnnWkW"
      "ZBe1e6AwLQ2MrvZ8cuTAj2-ChJKnKi8dAfMYyfroPNKHoBV7B9an9AmupEAT5LkRgKpFmBn8yUtFKPF-bCn-jDwztFig8YFFNIAb5DC0A3Q-nMU4FW"
      "11x6zj__ivaYEKblTt12FXRAhOh2qfeIk3bPQwOmg11lTDffr_BiYIwus7qJIUjxsa_Y1waSlHRiiQ045dHhcqKPsePN4ExtmkR9H6uVOEtT7MqLcX"
      "ZOxBb3OjoVfVeQpr5AdIAqV5KvSzdA3ZLXVVz-1Z9ArbH7elyc_w",
      394);
    v136 = 0.0;
    v137 = 0;
    if ( LOBYTE(v176) << 31 )
    {
      sub_EEE78(&v136, v177, HIDWORD(v176));
    }
    else
    {
      v137 = v177;
      v136 = v176;
    }
    sub_14D834(v134);
    sub_14D9A0(&v138, &v136, 1, v134);
    sub_169F6C(&v140, &v138);
    sub_16AF78(&v142, &v140);
    sub_16A0CC(&v140);
    if ( LOBYTE(v138) << 31 )
      operator delete(v139);
    if ( v134[0] << 31 )
      operator delete(v135);
    if ( LOBYTE(v136) << 31 )
      operator delete(v137);
    sub_169F0C(&v140, 0);
    sub_16AF78(&v142, &v140);
    v95 = sub_16A0CC(&v140);
    v96 = sub_EF6AC(v95);
    sub_F3626(&v140);
    v97 = (char *)v141;
    v98 = LOBYTE(v140);
    sub_F4168(&v138);
    v99 = (char *)v139;
    if ( !(v98 << 31) )
      v97 = (char *)&v140 + 1;
    if ( (LOBYTE(v138) & 1) == 0 )
      v99 = (char *)&v138 + 1;
    v100 = (*(int (__fastcall **)(int *, int, char *, char *, _DWORD, _DWORD))(*v121 + 452))(v121, v96, v97, v99, 0, 0);
    if ( LOBYTE(v138) << 31 )
      operator delete(v139);
    if ( LOBYTE(v140) << 31 )
      operator delete(v141);
    sub_16B404(&v140, &v142);
    v101 = (char *)v141;
    if ( (LOBYTE(v140) & 1) == 0 )
      v101 = (char *)&v140 + 1;
    v102 = (*(int (__fastcall **)(int *, char *))(*v121 + 668))(v121, v101);
    v103 = LOBYTE(v140) << 31;
    if ( v103 )
      operator delete(v141);
    v104 = sub_EF6AC(v103);
    sub_BEAEC(v121, v104, v100, v102);
    (*(void (__fastcall **)(int *, int))(*v121 + 92))(v121, v102);
    if ( LOBYTE(v176) << 31 )
      operator delete(v177);
    sub_16A0CC(&v142);
    v72 = -1;
  }
LABEL_205:
  v115 = v72;
LABEL_206:
  sub_CFD7C(v182);
  if ( (unsigned __int8)v180 << 31 )
    operator delete(v181);
  sub_16A0CC(v178);
  return v115;
}