#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RC4解密实现
基于sub_14D9A0函数的逻辑重现解密算法
"""

import base64
import time
import struct
from typing import List, Tuple

class RC4Decryptor:
    def __init__(self):
        # 从反汇编代码中提取的硬编码密钥（需要进一步分析确定具体值）
        self.key_constants = {
            'dword_14E8A0': b'\x00',  # 需要从二进制中提取实际值
            'dword_14E8A4': b'\x01',  # 需要从二进制中提取实际值
            'dword_14E8A8': b'\x02',  # 需要从二进制中提取实际值
            'dword_14E8AC': b'\x03',  # 需要从二进制中提取实际值
            'loc_14EA0C': b'\x04',    # 需要从二进制中提取实际值
            'loc_14EA08': b'\x05',    # 需要从二进制中提取实际值
            'loc_14EA14': b'\x06',    # 需要从二进制中提取实际值
            'loc_14EA10': b'\x07',    # 需要从二进制中提取实际值
            'dword_14EA20': b'\x08',  # 需要从二进制中提取实际值
        }
    
    def rc4_ksa(self, key: bytes) -> List[int]:
        """RC4密钥调度算法 (Key Scheduling Algorithm)"""
        S = list(range(256))
        j = 0
        
        for i in range(256):
            j = (j + S[i] + key[i % len(key)]) % 256
            S[i], S[j] = S[j], S[i]
        
        return S
    
    def rc4_prga(self, S: List[int], data: bytes) -> bytes:
        """RC4伪随机生成算法 (Pseudo-Random Generation Algorithm)"""
        i = j = 0
        result = bytearray()
        
        for byte in data:
            i = (i + 1) % 256
            j = (j + S[i]) % 256
            S[i], S[j] = S[j], S[i]
            k = S[(S[i] + S[j]) % 256]
            result.append(byte ^ k)
        
        return bytes(result)
    
    def rc4_decrypt(self, key: bytes, data: bytes) -> bytes:
        """完整的RC4解密"""
        S = self.rc4_ksa(key)
        return self.rc4_prga(S, data)
    
    def extract_key_from_data(self, encrypted_data: bytes, key_material: bytes, 
                            timestamp: int = None) -> bytes:
        """根据算法逻辑提取密钥"""
        if timestamp is None:
            timestamp = int(time.time())
        
        # 模拟算法中的密钥生成逻辑
        # 这里需要根据具体的sub_14C25C等函数的逻辑来实现
        
        # 简化版本：使用key_material的前16字节作为密钥
        key_part1 = key_material[:16] if len(key_material) >= 16 else key_material
        key_part2 = key_material[16:32] if len(key_material) >= 32 else key_material[:16]
        
        # 添加时间戳
        timestamp_bytes = struct.pack('<I', timestamp % 10000000000)  # 10位时间戳
        
        # 组合密钥
        combined_key = key_part1 + key_part2 + timestamp_bytes
        
        return combined_key
    
    def decrypt_with_hardcoded_keys(self, encrypted_data: bytes) -> List[Tuple[str, bytes]]:
        """使用硬编码的密钥尝试解密"""
        results = []

        # 从之前解码的Base64字符串中获取密钥
        hardcoded_keys = [
            # 主要解密字符串
            base64.urlsafe_b64decode("93bcRyt4K-RqujNG-oOp7owvp3fEAwM3ynsMeO2sY0RTlLRZAaZHpUcRB5mk21Zn1BL2AcYtRhRcxamU4Lc9gxuzqwhL9SVeUCigAIgbwZq_PmqA6bcfYf8I5AzN3HXNtj6QMtOtcmPTU8e2N-ppdGa6P1XYhkVGSHwed0R0lkC7GoJg2n27uIHHA_9V0qHolBPhgVEtSMxBWOdxJdzRdE5Ni00SxC6o" + "=="),

            # 条件分支字符串
            base64.urlsafe_b64decode("ca53r72r1VkvUAgT-M5hqFdt_5yn3vh7pGFteO1QVwr6tH9fKtmNooB9oTuTvv7xrg4H3qRpdcPL0YhhEZA" + "="),

            # case -5
            base64.b64decode("fc06Ud+2BZiej9EXs+qN34uGXYQbXrKsIJli0psK9CZ++r+/KtaTOBqe3OUijzID0OSAeEvst8anJg5SB52cejEYSORmiDHnZ2AnPTN7v8FrYVdH5bLazmUC2Sncms/SAZkU/2QFnl3JWNpxkNfM4A=="),

            # case -4
            base64.b64decode("59dbRs7sf8+AJUia1uF2xNaqbw9rBkmErddEUcb9Y+ldDup8JCPX/8rpBkq2eHIjaJzlBgTuAJ7FaypW9zvGSNTFmnJs124fCyFASJ2EmjwRTHWi7mTBPpAs9Tjo300dkX33HkaOI5IM4zbGciYoQlMGv2DH5QAK4CbTF8j+S3hvh+XmKIQ+4iNJMwx65pHfKsBsnu1gvP1RRKyFhmeMXg=="),

            # case -3
            base64.b64decode("eb6dlVYQMOYK60MlOQxwdthamPn1unh5DmQUmyPgcMQhSoji6dbD9voCnU0cVw4aATidBgPFVzB9sDUV96IPDPd17zSlA7XA5MyFmnryfw/K+YOHrRzXjKD4JPbqn45jLZrrbYvcSa72LZZTxl5vZQ=="),

            # 长解密字符串
            base64.b64decode("e40bjWQjoAF5Be0LKNg/O2qHobSxUld2EV2ATps3k/PAQF3sbWI+kIyEeA1g/PSxHq8VKP4r9YWMAdNS0h2vv3gKWGWZfQ9bmUdOvh0H21iRrnnWkWZBe1e6AwLQ2MrvZ8cuTAj2+ChJKnKi8dAfMYyfroPNKHoBV7B9an9AmupEAT5LkRgKpFmBn8yUtFKPF+bCn+jDwztFig8YFFNIAb5DC0A3Q+nMU4FW11x6zj//ivaYEKblTt12FXRAhOh2qfeIk3bPQwOmg11lTDffr/BiYIwus7qJIUjxsa/Y1waSlHRiiQ045dHhcqKPsePN4ExtmkR9H6uVOEtT7MqLcXZOxBb3OjoVfVeQpr5AdIAqV5KvSzdA3ZLXVVz+1Z9ArbH7elyc/w==")
        ]

        # 尝试简单的密钥
        simple_keys = [
            b"key", b"password", b"secret", b"dianjiqi", b"douyin",
            b"android", b"java", b"0123456789", b"abcdefghijklmnopqrstuvwxyz"
        ]

        # 首先尝试简单密钥
        for i, key in enumerate(simple_keys):
            try:
                decrypted = self.rc4_decrypt(key, encrypted_data)
                if self.is_valid_decryption(decrypted):
                    results.append((f"简单密钥_{key.decode()}", decrypted))
            except Exception as e:
                continue

        # 然后尝试硬编码密钥的不同部分
        for i, key_material in enumerate(hardcoded_keys):
            try:
                # 直接使用密钥材料
                decrypted = self.rc4_decrypt(key_material, encrypted_data)
                if self.is_valid_decryption(decrypted):
                    results.append((f"硬编码密钥{i+1}_直接", decrypted))

                # 使用密钥材料的前16字节
                if len(key_material) >= 16:
                    key_16 = key_material[:16]
                    decrypted = self.rc4_decrypt(key_16, encrypted_data)
                    if self.is_valid_decryption(decrypted):
                        results.append((f"硬编码密钥{i+1}_前16字节", decrypted))

                # 使用密钥材料的前32字节
                if len(key_material) >= 32:
                    key_32 = key_material[:32]
                    decrypted = self.rc4_decrypt(key_32, encrypted_data)
                    if self.is_valid_decryption(decrypted):
                        results.append((f"硬编码密钥{i+1}_前32字节", decrypted))

            except Exception as e:
                continue

        return results
    
    def is_valid_decryption(self, data: bytes) -> bool:
        """检查解密结果是否有效"""
        try:
            # 尝试解码为UTF-8
            text = data.decode('utf-8')
            
            # 检查是否包含相关关键词
            keywords = ['click', 'douyin', '点击', '抖音', 'action', 'script', 'delay', 'touch']
            if any(keyword.lower() in text.lower() for keyword in keywords):
                return True
                
            # 检查是否是JSON格式
            import json
            try:
                json.loads(text)
                return True
            except:
                pass
                
        except UnicodeDecodeError:
            pass
        
        # 检查可打印字符比例
        printable_count = sum(1 for b in data if 32 <= b <= 126)
        if len(data) > 0 and printable_count / len(data) > 0.7:
            return True
        
        return False

def main():
    print("=== RC4解密尝试 ===")
    
    # 读取加密的二进制数据
    try:
        with open("decoded_binary.bin", "rb") as f:
            encrypted_data = f.read()
        print(f"读取加密数据，长度: {len(encrypted_data)} 字节")
    except FileNotFoundError:
        print("未找到decoded_binary.bin文件，请先运行decrypt_analysis.py")
        return
    
    # 创建解密器
    decryptor = RC4Decryptor()
    
    # 尝试解密
    results = decryptor.decrypt_with_hardcoded_keys(encrypted_data)
    
    if results:
        print(f"\n找到 {len(results)} 个可能的解密结果:")
        for i, (description, decrypted_data) in enumerate(results):
            print(f"\n--- 结果 {i+1}: {description} ---")
            try:
                text = decrypted_data.decode('utf-8')
                print("解密文本:")
                print(text[:500] + "..." if len(text) > 500 else text)
                
                # 保存结果
                filename = f"decrypted_result_{i+1}_{description.replace(' ', '_')}.txt"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(text)
                print(f"已保存到: {filename}")
                
            except UnicodeDecodeError:
                print("二进制数据:")
                print(decrypted_data[:100].hex())
                
                # 保存二进制结果
                filename = f"decrypted_result_{i+1}_{description.replace(' ', '_')}.bin"
                with open(filename, 'wb') as f:
                    f.write(decrypted_data)
                print(f"已保存到: {filename}")
    else:
        print("未找到有效的解密结果")
        print("可能需要:")
        print("1. 获取正确的密钥常量值")
        print("2. 实现更精确的密钥生成算法")
        print("3. 调整RC4算法的实现细节")

if __name__ == "__main__":
    main()
