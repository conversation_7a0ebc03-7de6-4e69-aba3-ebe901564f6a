#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级解密分析工具
尝试多种解密方法来解密二进制数据
"""

import base64
import struct
import binascii

def read_binary_file(filename):
    """读取二进制文件"""
    try:
        with open(filename, 'rb') as f:
            return f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def xor_decrypt(data, key):
    """XOR解密"""
    if isinstance(key, str):
        key = key.encode()
    
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % len(key)])
    return bytes(result)

def try_xor_with_common_keys(data):
    """尝试常见的XOR密钥"""
    common_keys = [
        b'key',
        b'password',
        b'secret',
        b'dianjiqi',
        b'douyin',
        b'android',
        b'java',
        b'0123456789',
        b'abcdef<PERSON>ijklmnopqrstuvwxyz',
        b'\x01',
        b'\x02',
        b'\x03',
        b'\xff',
        b'\xaa',
        b'\x55',
    ]
    
    for key in common_keys:
        try:
            decrypted = xor_decrypt(data, key)
            # 检查是否包含可读文本
            try:
                text = decrypted.decode('utf-8')
                if any(keyword in text.lower() for keyword in ['click', 'douyin', '点击', '抖音', 'action', 'script']):
                    print(f"XOR密钥 {key} 可能有效:")
                    print(text[:200] + "..." if len(text) > 200 else text)
                    return decrypted
            except:
                pass
                
            # 检查是否包含可打印字符
            printable_count = sum(1 for b in decrypted[:1000] if 32 <= b <= 126)
            if printable_count > 500:  # 超过50%是可打印字符
                print(f"XOR密钥 {key} 产生了大量可打印字符:")
                try:
                    print(decrypted[:200].decode('utf-8', errors='ignore'))
                except:
                    print(decrypted[:200])
                    
        except Exception as e:
            continue
    
    return None

def try_caesar_cipher(data):
    """尝试凯撒密码"""
    for shift in range(1, 26):
        try:
            result = bytearray()
            for byte in data:
                if 65 <= byte <= 90:  # A-Z
                    result.append((byte - 65 + shift) % 26 + 65)
                elif 97 <= byte <= 122:  # a-z
                    result.append((byte - 97 + shift) % 26 + 97)
                else:
                    result.append(byte)
            
            text = bytes(result).decode('utf-8', errors='ignore')
            if any(keyword in text.lower() for keyword in ['click', 'douyin', '点击', '抖音']):
                print(f"凯撒密码偏移 {shift} 可能有效:")
                print(text[:200] + "..." if len(text) > 200 else text)
                return bytes(result)
        except:
            continue
    
    return None

def analyze_entropy(data):
    """分析数据熵"""
    from collections import Counter
    import math
    
    # 计算字节频率
    byte_counts = Counter(data)
    data_len = len(data)
    
    # 计算熵
    entropy = 0
    for count in byte_counts.values():
        probability = count / data_len
        entropy -= probability * math.log2(probability)
    
    print(f"数据熵: {entropy:.2f} (最大值: 8.0)")
    
    if entropy > 7.5:
        print("高熵值，可能是加密或压缩数据")
    elif entropy < 4.0:
        print("低熵值，可能包含重复模式或简单编码")
    else:
        print("中等熵值，可能是正常文本或轻度编码")

def try_reverse_operations(data):
    """尝试反向操作"""
    # 尝试反转字节序
    reversed_data = data[::-1]
    try:
        text = reversed_data.decode('utf-8')
        if any(keyword in text.lower() for keyword in ['click', 'douyin', '点击', '抖音']):
            print("反转字节序可能有效:")
            print(text[:200] + "..." if len(text) > 200 else text)
            return reversed_data
    except:
        pass
    
    # 尝试按位取反
    inverted_data = bytes(~b & 0xFF for b in data)
    try:
        text = inverted_data.decode('utf-8')
        if any(keyword in text.lower() for keyword in ['click', 'douyin', '点击', '抖音']):
            print("按位取反可能有效:")
            print(text[:200] + "..." if len(text) > 200 else text)
            return inverted_data
    except:
        pass
    
    return None

def try_block_operations(data):
    """尝试块操作"""
    # 尝试不同的块大小
    for block_size in [2, 4, 8, 16, 32]:
        if len(data) % block_size == 0:
            # 尝试交换块内字节序
            result = bytearray()
            for i in range(0, len(data), block_size):
                block = data[i:i+block_size]
                result.extend(block[::-1])  # 反转块内字节序
            
            try:
                text = bytes(result).decode('utf-8')
                if any(keyword in text.lower() for keyword in ['click', 'douyin', '点击', '抖音']):
                    print(f"块大小 {block_size} 反转可能有效:")
                    print(text[:200] + "..." if len(text) > 200 else text)
                    return bytes(result)
            except:
                pass
    
    return None

def main():
    filename = "decoded_binary.bin"
    
    print("=== 高级解密分析开始 ===")
    
    # 读取二进制文件
    data = read_binary_file(filename)
    if not data:
        return
    
    print(f"文件大小: {len(data)} 字节")
    
    # 分析数据熵
    print("\n=== 数据熵分析 ===")
    analyze_entropy(data)
    
    # 尝试XOR解密
    print("\n=== XOR解密尝试 ===")
    xor_result = try_xor_with_common_keys(data)
    
    # 尝试凯撒密码
    print("\n=== 凯撒密码尝试 ===")
    caesar_result = try_caesar_cipher(data)
    
    # 尝试反向操作
    print("\n=== 反向操作尝试 ===")
    reverse_result = try_reverse_operations(data)
    
    # 尝试块操作
    print("\n=== 块操作尝试 ===")
    block_result = try_block_operations(data)
    
    # 如果找到了可能的解密结果，保存它们
    results = []
    if xor_result:
        results.append(("xor", xor_result))
    if caesar_result:
        results.append(("caesar", caesar_result))
    if reverse_result:
        results.append(("reverse", reverse_result))
    if block_result:
        results.append(("block", block_result))
    
    for i, (method, result) in enumerate(results):
        filename = f"decrypted_{method}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(result.decode('utf-8', errors='ignore'))
            print(f"解密结果已保存到 {filename}")
        except:
            with open(filename, 'wb') as f:
                f.write(result)
            print(f"二进制解密结果已保存到 {filename}")

if __name__ == "__main__":
    main()
