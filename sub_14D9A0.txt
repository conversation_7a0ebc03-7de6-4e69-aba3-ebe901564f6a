int __fastcall sub_14D9A0(_DWORD *a1, unsigned __int8 *a2, int a3, unsigned __int8 *a4, int a5, time_t a6)
{
  time_t v7; // r11
  time_t v10; // r0
  double v11; // d16
  __int64 v12; // d16
  unsigned __int8 *v13; // r1
  double v14; // d16
  __int64 v15; // d16
  double v16; // d16
  double v17; // d16
  int v18; // r0
  double v19; // d16
  unsigned int v20; // r0
  void *v21; // r0
  unsigned int v22; // r1
  _BYTE *v23; // r2
  int v24; // r3
  int v25; // r0
  double v26; // d16
  int v27; // r4
  unsigned int v28; // r8
  unsigned int v29; // r5
  double v30; // d16
  size_t v31; // r0
  _BYTE *v32; // r1
  int v33; // r2
  int v34; // r0
  double v35; // d16
  int v36; // r4
  unsigned __int64 v37; // kr00_8
  unsigned int v38; // r0
  int v39; // r0
  __int64 v40; // d16
  int v41; // r11
  __int64 v42; // d16
  unsigned __int8 *v43; // r1
  unsigned int v44; // r0
  int v45; // r0
  int32x4_t v46; // q8
  int32x4_t v47; // q9
  int i; // r0
  int32x4_t *v49; // r1
  char *v50; // r5
  int j; // r4
  int v52; // r1
  int v53; // r0
  int v54; // r1
  int v55; // r2
  unsigned __int8 *v56; // r3
  int v57; // r4
  int v58; // r11
  int v59; // r9
  int v60; // r0
  unsigned __int8 *v61; // r1
  int v62; // r11
  const char *v63; // r0
  void *v64; // r0
  int v65; // r8
  int v66; // r6
  const char *v67; // r0
  _BYTE *v68; // r1
  int v69; // r2
  int v70; // r0
  double v71; // d16
  size_t v72; // r2
  int v73; // r0
  int v74; // r10
  unsigned __int8 *v75; // r1
  unsigned __int8 *v76; // r3
  int v77; // r0
  _BOOL4 v78; // r4
  int v80; // [sp+0h] [bp-B98h]
  void *v81; // [sp+4h] [bp-B94h]
  _DWORD *v82; // [sp+8h] [bp-B90h]
  int v83; // [sp+Ch] [bp-B8Ch]
  int v84; // [sp+10h] [bp-B88h]
  int v85; // [sp+14h] [bp-B84h]
  unsigned __int8 *v86; // [sp+18h] [bp-B80h]
  int v87; // [sp+1Ch] [bp-B7Ch]
  _DWORD v88[2]; // [sp+20h] [bp-B78h] BYREF
  void *v89; // [sp+28h] [bp-B70h]
  _DWORD v90[2]; // [sp+30h] [bp-B68h] BYREF
  void *v91; // [sp+38h] [bp-B60h]
  double v92; // [sp+40h] [bp-B58h] BYREF
  void *v93; // [sp+48h] [bp-B50h]
  _DWORD v94[2]; // [sp+50h] [bp-B48h] BYREF
  void *v95; // [sp+58h] [bp-B40h]
  _DWORD v96[2]; // [sp+60h] [bp-B38h] BYREF
  void *v97; // [sp+68h] [bp-B30h]
  double v98; // [sp+70h] [bp-B28h] BYREF
  void *v99; // [sp+78h] [bp-B20h]
  _DWORD v100[2]; // [sp+80h] [bp-B18h] BYREF
  void *v101; // [sp+88h] [bp-B10h]
  _DWORD v102[2]; // [sp+90h] [bp-B08h] BYREF
  void *v103; // [sp+98h] [bp-B00h]
  double v104; // [sp+A0h] [bp-AF8h] BYREF
  void *v105; // [sp+A8h] [bp-AF0h]
  double v106; // [sp+B0h] [bp-AE8h] BYREF
  void *v107; // [sp+B8h] [bp-AE0h]
  double v108; // [sp+C0h] [bp-AD8h] BYREF
  void *v109; // [sp+C8h] [bp-AD0h]
  double v110; // [sp+D0h] [bp-AC8h] BYREF
  void *v111; // [sp+D8h] [bp-AC0h]
  _BYTE v112[8]; // [sp+DCh] [bp-ABCh] BYREF
  void *v113; // [sp+E4h] [bp-AB4h]
  double v114; // [sp+E8h] [bp-AB0h] BYREF
  void *v115; // [sp+F0h] [bp-AA8h]
  _BYTE v116[8]; // [sp+F8h] [bp-AA0h] BYREF
  void *v117; // [sp+100h] [bp-A98h]
  unsigned __int8 v118; // [sp+104h] [bp-A94h] BYREF
  _BYTE v119[7]; // [sp+105h] [bp-A93h] BYREF
  void *v120; // [sp+10Ch] [bp-A8Ch]
  double v121; // [sp+110h] [bp-A88h] BYREF
  void *v122; // [sp+118h] [bp-A80h]
  unsigned __int8 v123; // [sp+11Ch] [bp-A7Ch] BYREF
  _BYTE v124[7]; // [sp+11Dh] [bp-A7Bh] BYREF
  void *v125; // [sp+124h] [bp-A74h]
  double v126; // [sp+128h] [bp-A70h] BYREF
  void *v127; // [sp+130h] [bp-A68h]
  double v128; // [sp+138h] [bp-A60h] BYREF
  void *v129; // [sp+140h] [bp-A58h]
  double v130; // [sp+148h] [bp-A50h] BYREF
  void *v131; // [sp+150h] [bp-A48h]
  double v132; // [sp+158h] [bp-A40h] BYREF
  void *v133; // [sp+160h] [bp-A38h]
  double v134; // [sp+168h] [bp-A30h] BYREF
  void *v135; // [sp+170h] [bp-A28h]
  _BYTE v136[8]; // [sp+174h] [bp-A24h] BYREF
  void *v137; // [sp+17Ch] [bp-A1Ch]
  _BYTE v138[8]; // [sp+180h] [bp-A18h] BYREF
  void *v139; // [sp+188h] [bp-A10h]
  unsigned __int8 v140; // [sp+18Ch] [bp-A0Ch] BYREF
  _BYTE v141[7]; // [sp+18Dh] [bp-A0Bh] BYREF
  void *v142; // [sp+194h] [bp-A04h]
  _DWORD v143[2]; // [sp+198h] [bp-A00h] BYREF
  void *v144; // [sp+1A0h] [bp-9F8h]
  double v145; // [sp+1A8h] [bp-9F0h] BYREF
  void *v146; // [sp+1B0h] [bp-9E8h]
  double v147; // [sp+1B8h] [bp-9E0h] BYREF
  void *v148; // [sp+1C0h] [bp-9D8h]
  _BYTE v149[8]; // [sp+1C8h] [bp-9D0h] BYREF
  void *v150; // [sp+1D0h] [bp-9C8h]
  _BYTE v151[8]; // [sp+1D4h] [bp-9C4h] BYREF
  void *v152; // [sp+1DCh] [bp-9BCh]
  double v153; // [sp+1E0h] [bp-9B8h] BYREF
  char *v154; // [sp+1E8h] [bp-9B0h]
  double v155; // [sp+1F0h] [bp-9A8h] BYREF
  void *v156; // [sp+1F8h] [bp-9A0h]
  double v157; // [sp+200h] [bp-998h] BYREF
  void *v158; // [sp+208h] [bp-990h]
  _BYTE v159[8]; // [sp+210h] [bp-988h] BYREF
  void *v160; // [sp+218h] [bp-980h]
  _BYTE v161[8]; // [sp+21Ch] [bp-97Ch] BYREF
  void *v162; // [sp+224h] [bp-974h]
  double v163; // [sp+228h] [bp-970h] BYREF
  void *v164; // [sp+230h] [bp-968h]
  double v165; // [sp+238h] [bp-960h] BYREF
  void *v166; // [sp+240h] [bp-958h]
  _BYTE v167[8]; // [sp+248h] [bp-950h] BYREF
  void *v168; // [sp+250h] [bp-948h]
  unsigned __int8 v169; // [sp+254h] [bp-944h] BYREF
  _BYTE v170[7]; // [sp+255h] [bp-943h] BYREF
  void *v171; // [sp+25Ch] [bp-93Ch]
  double v172; // [sp+260h] [bp-938h] BYREF
  void *v173; // [sp+268h] [bp-930h]
  _BYTE v174[8]; // [sp+270h] [bp-928h] BYREF
  void *v175; // [sp+278h] [bp-920h]
  unsigned __int8 v176; // [sp+27Ch] [bp-91Ch] BYREF
  _BYTE v177[7]; // [sp+27Dh] [bp-91Bh] BYREF
  void *v178; // [sp+284h] [bp-914h]
  _DWORD v179[2]; // [sp+288h] [bp-910h] BYREF
  void *v180; // [sp+290h] [bp-908h]
  _DWORD v181[2]; // [sp+298h] [bp-900h] BYREF
  void *v182; // [sp+2A0h] [bp-8F8h]
  double v183; // [sp+2A8h] [bp-8F0h] BYREF
  void *v184; // [sp+2B0h] [bp-8E8h]
  _DWORD v185[2]; // [sp+2B8h] [bp-8E0h] BYREF
  void *v186; // [sp+2C0h] [bp-8D8h]
  _DWORD v187[2]; // [sp+2C8h] [bp-8D0h] BYREF
  void *v188; // [sp+2D0h] [bp-8C8h]
  double v189; // [sp+2D8h] [bp-8C0h] BYREF
  void *v190; // [sp+2E0h] [bp-8B8h]
  _DWORD v191[256]; // [sp+2E8h] [bp-8B0h]
  __int64 v192; // [sp+6E8h] [bp-4B0h] BYREF
  void *v193; // [sp+6F0h] [bp-4A8h]
  char s[32]; // [sp+AE8h] [bp-B0h] BYREF
  _BYTE v195[144]; // [sp+B08h] [bp-90h] BYREF

  v7 = a6;
  v86 = a2;
  if ( !a6 )
  {
    v10 = time(0);
    a2 = v86;
    v7 = v10;
  }
  if ( a3 == 1 )
  {
    v189 = 0.0;
    v190 = 0;
    if ( *a2 << 31 )
    {
      sub_EEE78(&v189, *((_DWORD *)a2 + 2), *((_DWORD *)a2 + 1));
    }
    else
    {
      v11 = *(double *)a2;
      v190 = (void *)*((_DWORD *)a2 + 2);
      v189 = v11;
    }
    v187[0] = 0;
    v187[1] = 0;
    v188 = 0;
    sub_EEE78(v187, &dword_14E8A0, 1);
    v185[0] = 0;
    v185[1] = 0;
    v186 = 0;
    sub_EEE78(v185, &dword_14E8A4, 1);
    sub_14CDC2(&v192, &v189, v187, v185);
    if ( *v86 << 31 )
    {
      **((_BYTE **)v86 + 2) = 0;
      *((_DWORD *)v86 + 1) = 0;
    }
    else
    {
      *(_WORD *)v86 = 0;
    }
    sub_EF064(v86, 0);
    v12 = v192;
    v13 = v86;
    *((_DWORD *)v86 + 2) = v193;
    *(_QWORD *)v86 = v12;
    v192 = 0LL;
    v193 = 0;
    if ( LOBYTE(v185[0]) << 31 )
    {
      operator delete(v186);
      v13 = v86;
    }
    if ( LOBYTE(v187[0]) << 31 )
    {
      operator delete(v188);
      v13 = v86;
    }
    if ( LOBYTE(v189) << 31 )
    {
      operator delete(v190);
      v13 = v86;
    }
    v183 = 0.0;
    v184 = 0;
    if ( *v13 << 31 )
    {
      sub_EEE78(&v183, *((_DWORD *)v13 + 2), *((_DWORD *)v13 + 1));
    }
    else
    {
      v14 = *(double *)v13;
      v184 = (void *)*((_DWORD *)v13 + 2);
      v183 = v14;
    }
    v181[0] = 0;
    v181[1] = 0;
    v182 = 0;
    sub_EEE78(v181, &dword_14E8A8, 1);
    v179[0] = 0;
    v179[1] = 0;
    v180 = 0;
    sub_EEE78(v179, dword_14E8AC, 1);
    sub_14CDC2(&v192, &v183, v181, v179);
    if ( *v86 << 31 )
    {
      **((_BYTE **)v86 + 2) = 0;
      *((_DWORD *)v86 + 1) = 0;
    }
    else
    {
      *(_WORD *)v86 = 0;
    }
    sub_EF064(v86, 0);
    v15 = v192;
    *((_DWORD *)v86 + 2) = v193;
    *(_QWORD *)v86 = v15;
    v192 = 0LL;
    v193 = 0;
    if ( LOBYTE(v179[0]) << 31 )
      operator delete(v180);
    if ( LOBYTE(v181[0]) << 31 )
      operator delete(v182);
    if ( LOBYTE(v183) << 31 )
      operator delete(v184);
  }
  nullsub_1(v195);
  v172 = 0.0;
  v173 = 0;
  if ( *a4 << 31 )
  {
    sub_EEE78(&v172, *((_DWORD *)a4 + 2), *((_DWORD *)a4 + 1));
  }
  else
  {
    v16 = *(double *)a4;
    v173 = (void *)*((_DWORD *)a4 + 2);
    v172 = v16;
  }
  sub_14C25C(v174, &v172, 0, 16);
  sub_1666A8(&v176, v195, v174);
  if ( v174[0] << 31 )
    operator delete(v175);
  if ( LOBYTE(v172) << 31 )
    operator delete(v173);
  v165 = 0.0;
  v166 = 0;
  if ( *a4 << 31 )
  {
    sub_EEE78(&v165, *((_DWORD *)a4 + 2), *((_DWORD *)a4 + 1));
  }
  else
  {
    v17 = *(double *)a4;
    v166 = (void *)*((_DWORD *)a4 + 2);
    v165 = v17;
  }
  sub_14C25C(v167, &v165, 16, 16);
  sub_1666A8(&v169, v195, v167);
  if ( v167[0] << 31 )
    operator delete(v168);
  if ( LOBYTE(v165) << 31 )
    operator delete(v166);
  v18 = sub_14AC24(1, 100);
  sub_14B2A4(v161, v18 + v7);
  sub_1666A8(&v163, v195, v161);
  if ( v161[0] << 31 )
    operator delete(v162);
  if ( a3 == 1 )
  {
    v157 = 0.0;
    v158 = 0;
    if ( *v86 << 31 )
    {
      sub_EEE78(&v157, *((_DWORD *)v86 + 2), *((_DWORD *)v86 + 1));
    }
    else
    {
      v19 = *(double *)v86;
      v158 = (void *)*((_DWORD *)v86 + 2);
      v157 = v19;
    }
    sub_14C25C(v159, &v157, 0, 4);
    if ( LOBYTE(v157) << 31 )
    {
      v21 = v158;
LABEL_57:
      operator delete(v21);
    }
  }
  else
  {
    v20 = LOBYTE(v163);
    v155 = 0.0;
    v156 = 0;
    if ( (LOBYTE(v163) & 1) != 0 )
    {
      sub_EEE78(&v155, v164, HIDWORD(v163));
      v20 = LOBYTE(v163);
    }
    else
    {
      v156 = v164;
      v155 = v163;
    }
    v22 = HIDWORD(v163);
    if ( (v20 & 1) == 0 )
      v22 = v20 >> 1;
    sub_14C25C(v159, &v155, v22 - 4, -1);
    if ( LOBYTE(v155) << 31 )
    {
      v21 = v156;
      goto LABEL_57;
    }
  }
  sub_D2E08(v151, &v176, v159);
  sub_1666A8(&v192, v195, v151);
  v24 = *(_DWORD *)&v177[3];
  v23 = v178;
  if ( (v176 & 1) == 0 )
  {
    v23 = v177;
    v24 = v176 >> 1;
  }
  v25 = sub_EF158(&v192, 0, v23, v24);
  v26 = *(double *)v25;
  v154 = *(char **)(v25 + 8);
  v153 = v26;
  *(_DWORD *)v25 = 0;
  *(_DWORD *)(v25 + 4) = 0;
  *(_DWORD *)(v25 + 8) = 0;
  if ( (unsigned __int8)v192 << 31 )
    operator delete(v193);
  v82 = a1;
  v83 = a3;
  v27 = a5;
  if ( v151[0] << 31 )
    operator delete(v152);
  v28 = HIDWORD(v153);
  v29 = LOBYTE(v153);
  if ( a5 )
    v27 = a5 + v7;
  snprintf(s, 0x20u, "%010d", v27, v7);
  if ( (v29 & 1) == 0 )
    v28 = v29 >> 1;
  v84 = v29 & 1;
  if ( v83 == 1 )
  {
    v147 = 0.0;
    v148 = 0;
    if ( *v86 << 31 )
    {
      sub_EEE78(&v147, *((_DWORD *)v86 + 2), *((_DWORD *)v86 + 1));
    }
    else
    {
      v30 = *(double *)v86;
      v148 = (void *)*((_DWORD *)v86 + 2);
      v147 = v30;
    }
    sub_14C25C(v149, &v147, 4, -1);
    sub_166D58(&v192, v149);
    v41 = 0;
    v36 = 1;
  }
  else
  {
    v143[0] = 0;
    v143[1] = 0;
    v144 = 0;
    v31 = strlen(s);
    sub_EEE78(v143, s, v31);
    sub_D2E08(v136, v86, &v169);
    sub_1666A8(v138, v195, v136);
    sub_14C25C(&v140, v138, 0, 16);
    v33 = *(_DWORD *)&v141[3];
    v32 = v142;
    if ( (v140 & 1) == 0 )
    {
      v32 = v141;
      v33 = v140 >> 1;
    }
    v34 = sub_EF260(v143, v32, v33);
    v35 = *(double *)v34;
    v36 = 0;
    v146 = *(void **)(v34 + 8);
    v145 = v35;
    *(_DWORD *)v34 = 0;
    *(_DWORD *)(v34 + 4) = 0;
    *(_DWORD *)(v34 + 8) = 0;
    v37 = *(_QWORD *)(v86 + 4);
    v38 = *v86;
    if ( (v38 & 1) == 0 )
      v37 = __PAIR64__((unsigned int)(v86 + 1), v38 >> 1);
    v39 = sub_EF260(&v145, HIDWORD(v37), v37);
    v40 = *(_QWORD *)v39;
    v41 = 1;
    v193 = *(void **)(v39 + 8);
    v192 = v40;
    *(_DWORD *)v39 = 0;
    *(_DWORD *)(v39 + 4) = 0;
    *(_DWORD *)(v39 + 8) = 0;
  }
  if ( *v86 << 31 )
  {
    **((_BYTE **)v86 + 2) = 0;
    *((_DWORD *)v86 + 1) = 0;
  }
  else
  {
    *(_WORD *)v86 = 0;
  }
  sub_EF064(v86, 0);
  v42 = v192;
  v43 = v86;
  *((_DWORD *)v86 + 2) = v193;
  *(_QWORD *)v86 = v42;
  v193 = 0;
  v192 = 0LL;
  if ( v41 )
  {
    if ( LOBYTE(v145) << 31 )
    {
      operator delete(v146);
      v43 = v86;
    }
    if ( v140 << 31 )
    {
      operator delete(v142);
      v43 = v86;
    }
    if ( v138[0] << 31 )
    {
      operator delete(v139);
      v43 = v86;
    }
    if ( v136[0] << 31 )
    {
      operator delete(v137);
      v43 = v86;
    }
    if ( LOBYTE(v143[0]) << 31 )
    {
      operator delete(v144);
      v43 = v86;
    }
  }
  if ( v36 == 1 )
  {
    if ( v149[0] << 31 )
    {
      operator delete(v150);
      v43 = v86;
    }
    if ( LOBYTE(v147) << 31 )
    {
      operator delete(v148);
      v43 = v86;
    }
  }
  v44 = *v43;
  if ( (v44 & 1) != 0 )
    v45 = *((_DWORD *)v43 + 1);
  else
    v45 = v44 >> 1;
  v85 = v45;
  v134 = 0.0;
  v135 = 0;
  sub_EEE78(&v134, &unk_57D240, 0);
  v46.n128_u64[0] = 0x400000004LL;
  v46.n128_u64[1] = 0x400000004LL;
  v47.n128_u64[0] = 0x100000000LL;
  v47.n128_u64[1] = 0x300000002LL;
  for ( i = 0; i != 1024; i += 16 )
  {
    v49 = (int32x4_t *)((char *)&v192 + i);
    *v49 = v47;
    v47 = vaddq_s32(v47, v46);
  }
  v50 = v154;
  v81 = v154;
  if ( !v84 )
    v50 = (char *)&v153 + 1;
  for ( j = 0; j != 256; ++j )
  {
    sub_506B88(j, v28);
    v191[j] = (unsigned __int8)v50[v52];
  }
  v53 = 0;
  v54 = 0;
  do
  {
    v55 = *((_DWORD *)&v192 + v53);
    v54 = (v54 + v55 + v191[v53]) % 256;
    *((_DWORD *)&v192 + v53++) = *((_DWORD *)&v192 + v54);
    *((_DWORD *)&v192 + v54) = v55;
  }
  while ( v53 != 256 );
  v56 = v86;
  if ( v85 >= 1 )
  {
    v57 = 0;
    v58 = 0;
    v59 = 0;
    do
    {
      v59 = (v59 + 1) % 256;
      v60 = *((_DWORD *)&v192 + v59);
      v58 = (v60 + v58) % 256;
      *((_DWORD *)&v192 + v59) = *((_DWORD *)&v192 + v58);
      *((_DWORD *)&v192 + v58) = v60;
      v61 = v86 + 1;
      if ( *v56 << 31 )
        v61 = (unsigned __int8 *)*((_DWORD *)v56 + 2);
      sub_146B5C(
        &v134,
        *((_BYTE *)&v192
        + 4
        * (v60
         + *((_DWORD *)&v192 + v59)
         - ((v60 + *((_DWORD *)&v192 + v59) + ((unsigned int)((v60 + *((_DWORD *)&v192 + v59)) >> 31) >> 24)) & 0x3FFFFF00))) ^ v61[v57++]);
      v56 = v86;
    }
    while ( v57 < v85 );
  }
  if ( v83 == 1 )
  {
    v130 = 0.0;
    v131 = 0;
    if ( LOBYTE(v134) << 31 )
    {
      sub_EEE78(&v130, v135, HIDWORD(v134));
    }
    else
    {
      v131 = v135;
      v130 = v134;
    }
    v62 = v84;
    sub_14C25C(&v132, &v130, 0, 10);
    v63 = (const char *)v133;
    if ( (LOBYTE(v132) & 1) == 0 )
      v63 = (char *)&v132 + 1;
    if ( atoi(v63) )
    {
      v126 = 0.0;
      v127 = 0;
      if ( LOBYTE(v134) << 31 )
      {
        sub_EEE78(&v126, v135, HIDWORD(v134));
      }
      else
      {
        v127 = v135;
        v126 = v134;
      }
      sub_14C25C(&v128, &v126, 0, 10);
      v66 = LOBYTE(v128);
      v67 = (const char *)v129;
      if ( (LOBYTE(v128) & 1) == 0 )
        v67 = (char *)&v128 + 1;
      if ( atoi(v67) <= v80 )
      {
        v78 = 0;
        goto LABEL_213;
      }
      v65 = 1;
    }
    else
    {
      v65 = 0;
    }
    v121 = 0.0;
    v122 = 0;
    if ( LOBYTE(v134) << 31 )
    {
      sub_EEE78(&v121, v135, HIDWORD(v134));
    }
    else
    {
      v122 = v135;
      v121 = v134;
    }
    sub_14C25C(&v123, &v121, 10, 16);
    v110 = 0.0;
    v111 = 0;
    if ( LOBYTE(v134) << 31 )
    {
      sub_EEE78(&v110, v135, HIDWORD(v134));
    }
    else
    {
      v111 = v135;
      v110 = v134;
    }
    sub_14C25C(v112, &v110, 26, -1);
    v69 = *(_DWORD *)&v170[3];
    v68 = v171;
    if ( (v169 & 1) == 0 )
    {
      v68 = v170;
      v69 = v169 >> 1;
    }
    v70 = sub_EF260(v112, v68, v69);
    v71 = *(double *)v70;
    v115 = *(void **)(v70 + 8);
    v114 = v71;
    *(_DWORD *)v70 = 0;
    *(_DWORD *)(v70 + 4) = 0;
    *(_DWORD *)(v70 + 8) = 0;
    sub_1666A8(v116, v195, &v114);
    sub_14C25C(&v118, v116, 0, 16);
    v72 = *(_DWORD *)&v124[3];
    v73 = *(_DWORD *)&v119[3];
    if ( (v123 & 1) == 0 )
      v72 = v123 >> 1;
    v74 = v118 & 1;
    if ( (v118 & 1) == 0 )
      v73 = v118 >> 1;
    if ( v72 != v73 )
      goto LABEL_192;
    v75 = (unsigned __int8 *)v120;
    v76 = v124;
    if ( (v118 & 1) == 0 )
      v75 = v119;
    if ( (v123 & 1) != 0 )
    {
      if ( v72 )
      {
        v78 = memcmp(v125, v75, v72) == 0;
        goto LABEL_197;
      }
    }
    else if ( v72 )
    {
      v77 = -(v123 >> 1);
      while ( *v76 == *v75 )
      {
        ++v77;
        ++v75;
        ++v76;
        if ( !v77 )
          goto LABEL_196;
      }
LABEL_192:
      v78 = 0;
LABEL_197:
      if ( v74 )
        operator delete(v120);
      if ( v116[0] << 31 )
        operator delete(v117);
      if ( LOBYTE(v114) << 31 )
        operator delete(v115);
      if ( v112[0] << 31 )
        operator delete(v113);
      if ( LOBYTE(v110) << 31 )
        operator delete(v111);
      if ( v123 << 31 )
        operator delete(v125);
      if ( LOBYTE(v121) << 31 )
        operator delete(v122);
      if ( v65 != 1 )
      {
LABEL_217:
        if ( LOBYTE(v132) << 31 )
          operator delete(v133);
        if ( LOBYTE(v130) << 31 )
          operator delete(v131);
        if ( !v78 )
        {
          *v82 = 0;
          v82[1] = 0;
          v82[2] = 0;
          sub_EEE78(v82, &unk_57D240, 0);
          goto LABEL_229;
        }
        v108 = 0.0;
        v109 = 0;
        if ( LOBYTE(v134) << 31 )
        {
          sub_EEE78(&v108, v135, HIDWORD(v134));
        }
        else
        {
          v109 = v135;
          v108 = v134;
        }
        sub_14C25C(v82, &v108, 26, -1);
        if ( LOBYTE(v108) << 31 )
        {
          v64 = v109;
          goto LABEL_228;
        }
        goto LABEL_229;
      }
      v66 = LOBYTE(v128);
LABEL_213:
      if ( v66 << 31 )
        operator delete(v129);
      if ( LOBYTE(v126) << 31 )
        operator delete(v127);
      goto LABEL_217;
    }
LABEL_196:
    v78 = 1;
    goto LABEL_197;
  }
  v106 = 0.0;
  v107 = 0;
  if ( LOBYTE(v134) << 31 )
  {
    v62 = v84;
    sub_EEE78(&v106, v135, HIDWORD(v134));
  }
  else
  {
    v107 = v135;
    v106 = v134;
    v62 = v84;
  }
  sub_166CC0(&v132, &v106);
  if ( LOBYTE(v106) << 31 )
    operator delete(v107);
  v104 = 0.0;
  v105 = 0;
  if ( LOBYTE(v132) << 31 )
  {
    sub_EEE78(&v104, v133, HIDWORD(v132));
  }
  else
  {
    v105 = v133;
    v104 = v132;
  }
  v102[0] = 0;
  v102[1] = 0;
  v103 = 0;
  sub_EEE78(v102, &loc_14EA0C, 1);
  v100[0] = 0;
  v100[1] = 0;
  v101 = 0;
  sub_EEE78(v100, &loc_14EA08, 1);
  sub_14CDC2(&v128, &v104, v102, v100);
  if ( LOBYTE(v132) << 31 )
  {
    *(_BYTE *)v133 = 0;
    HIDWORD(v132) = 0;
  }
  else
  {
    LOWORD(v132) = 0;
  }
  sub_EF064(&v132, 0);
  v132 = v128;
  v133 = v129;
  v128 = 0.0;
  v129 = 0;
  if ( LOBYTE(v100[0]) << 31 )
    operator delete(v101);
  if ( LOBYTE(v102[0]) << 31 )
    operator delete(v103);
  if ( LOBYTE(v104) << 31 )
    operator delete(v105);
  v98 = 0.0;
  v99 = 0;
  if ( LOBYTE(v132) << 31 )
  {
    sub_EEE78(&v98, v133, HIDWORD(v132));
  }
  else
  {
    v99 = v133;
    v98 = v132;
  }
  v96[0] = 0;
  v96[1] = 0;
  v97 = 0;
  sub_EEE78(v96, &loc_14EA14, 1);
  v94[0] = 0;
  v94[1] = 0;
  v95 = 0;
  sub_EEE78(v94, &loc_14EA10, 1);
  sub_14CDC2(&v128, &v98, v96, v94);
  if ( LOBYTE(v132) << 31 )
  {
    *(_BYTE *)v133 = 0;
    HIDWORD(v132) = 0;
  }
  else
  {
    LOWORD(v132) = 0;
  }
  sub_EF064(&v132, 0);
  v132 = v128;
  v133 = v129;
  v128 = 0.0;
  v129 = 0;
  if ( LOBYTE(v94[0]) << 31 )
    operator delete(v95);
  if ( LOBYTE(v96[0]) << 31 )
    operator delete(v97);
  if ( LOBYTE(v98) << 31 )
    operator delete(v99);
  v92 = 0.0;
  v93 = 0;
  if ( LOBYTE(v132) << 31 )
  {
    sub_EEE78(&v92, v133, HIDWORD(v132));
  }
  else
  {
    v93 = v133;
    v92 = v132;
  }
  v90[0] = 0;
  v90[1] = 0;
  v91 = 0;
  sub_EEE78(v90, &dword_14EA20, 1);
  v88[0] = 0;
  v88[1] = 0;
  v89 = 0;
  sub_EEE78(v88, &unk_57D240, 0);
  sub_14CDC2(&v128, &v92, v90, v88);
  if ( LOBYTE(v132) << 31 )
  {
    *(_BYTE *)v133 = 0;
    HIDWORD(v132) = 0;
  }
  else
  {
    LOWORD(v132) = 0;
  }
  sub_EF064(&v132, 0);
  v132 = v128;
  v133 = v129;
  v128 = 0.0;
  v129 = 0;
  if ( LOBYTE(v88[0]) << 31 )
    operator delete(v89);
  if ( LOBYTE(v90[0]) << 31 )
    operator delete(v91);
  if ( LOBYTE(v92) << 31 )
    operator delete(v93);
  sub_D2E08(a1, v159, &v132);
  if ( LOBYTE(v132) << 31 )
  {
    v64 = v133;
LABEL_228:
    operator delete(v64);
  }
LABEL_229:
  if ( LOBYTE(v134) << 31 )
    operator delete(v135);
  if ( v62 )
    operator delete(v81);
  if ( v159[0] << 31 )
    operator delete(v160);
  if ( LOBYTE(v163) << 31 )
    operator delete(v164);
  if ( v169 << 31 )
    operator delete(v171);
  if ( v176 << 31 )
    operator delete(v178);
  return _stack_chk_guard - v87;
}