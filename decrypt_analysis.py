#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解密分析工具
用于分析和解密"抖音定时（蓝天版）.txt"文件
"""

import base64
import binascii
import json
import re

def read_encrypted_file(filename):
    """读取加密文件内容"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        return content
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def analyze_string_format(content):
    """分析字符串格式"""
    print(f"字符串长度: {len(content)}")
    print(f"前50个字符: {content[:50]}")
    print(f"后50个字符: {content[-50:]}")
    
    # 检查是否是Base64格式
    base64_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=')
    is_base64_like = all(c in base64_chars for c in content)
    print(f"看起来像Base64: {is_base64_like}")
    
    # 检查字符分布
    char_count = {}
    for c in content:
        char_count[c] = char_count.get(c, 0) + 1
    
    print("字符频率分析:")
    sorted_chars = sorted(char_count.items(), key=lambda x: x[1], reverse=True)
    for char, count in sorted_chars[:10]:
        print(f"  '{char}': {count} 次")

def try_base64_decode(content):
    """尝试Base64解码"""
    try:
        # 尝试直接解码
        decoded = base64.b64decode(content)
        print(f"Base64解码成功，长度: {len(decoded)}")
        
        # 检查解码后的内容
        try:
            # 尝试作为UTF-8文本
            text = decoded.decode('utf-8')
            print("解码为UTF-8文本成功:")
            print(text[:200] + "..." if len(text) > 200 else text)
            return text
        except UnicodeDecodeError:
            print("解码后不是有效的UTF-8文本")
            
        # 检查是否是二进制数据
        print(f"二进制数据前100字节: {decoded[:100]}")
        
        # 检查是否包含可打印字符
        printable_count = sum(1 for b in decoded if 32 <= b <= 126)
        print(f"可打印字符比例: {printable_count/len(decoded)*100:.2f}%")
        
        return decoded
        
    except Exception as e:
        print(f"Base64解码失败: {e}")
        return None

def try_url_decode(content):
    """尝试URL解码"""
    try:
        import urllib.parse
        decoded = urllib.parse.unquote(content)
        if decoded != content:
            print("URL解码后有变化:")
            print(decoded[:200] + "..." if len(decoded) > 200 else decoded)
            return decoded
        else:
            print("URL解码后无变化")
            return None
    except Exception as e:
        print(f"URL解码失败: {e}")
        return None

def analyze_pattern(content):
    """分析字符串模式"""
    # 查找重复模式
    patterns = []
    for length in [2, 3, 4, 5, 6]:
        for i in range(len(content) - length):
            pattern = content[i:i+length]
            count = content.count(pattern)
            if count > 3:  # 出现3次以上的模式
                patterns.append((pattern, count))
    
    if patterns:
        print("发现的重复模式:")
        # 去重并排序
        unique_patterns = list(set(patterns))
        unique_patterns.sort(key=lambda x: x[1], reverse=True)
        for pattern, count in unique_patterns[:10]:
            print(f"  '{pattern}': {count} 次")

def analyze_binary_data(data):
    """分析二进制数据"""
    print(f"二进制数据长度: {len(data)}")

    # 检查文件头
    if len(data) >= 4:
        header = data[:4]
        print(f"文件头: {header.hex()} ({header})")

        # 检查常见文件格式
        if header.startswith(b'PK'):
            print("可能是ZIP格式文件")
        elif header.startswith(b'\x1f\x8b'):
            print("可能是GZIP格式文件")
        elif header.startswith(b'BZh'):
            print("可能是BZIP2格式文件")

    # 查找可能的字符串
    try:
        # 尝试查找ASCII字符串
        strings = []
        current_string = ""
        for byte in data:
            if 32 <= byte <= 126:  # 可打印ASCII字符
                current_string += chr(byte)
            else:
                if len(current_string) >= 4:  # 至少4个字符的字符串
                    strings.append(current_string)
                current_string = ""

        if current_string and len(current_string) >= 4:
            strings.append(current_string)

        if strings:
            print(f"发现 {len(strings)} 个可能的字符串:")
            for i, s in enumerate(strings[:20]):  # 只显示前20个
                print(f"  {i+1}: {s}")

    except Exception as e:
        print(f"字符串提取失败: {e}")

    # 尝试不同的解压缩方法
    try_decompress(data)

def try_decompress(data):
    """尝试解压缩数据"""
    import gzip
    import zlib

    # 尝试gzip解压
    try:
        decompressed = gzip.decompress(data)
        print(f"GZIP解压成功，长度: {len(decompressed)}")
        try:
            text = decompressed.decode('utf-8')
            print("GZIP解压后的文本:")
            print(text[:500] + "..." if len(text) > 500 else text)
            return decompressed
        except:
            print("GZIP解压后不是UTF-8文本")
    except Exception as e:
        print(f"GZIP解压失败: {e}")

    # 尝试zlib解压
    try:
        decompressed = zlib.decompress(data)
        print(f"ZLIB解压成功，长度: {len(decompressed)}")
        try:
            text = decompressed.decode('utf-8')
            print("ZLIB解压后的文本:")
            print(text[:500] + "..." if len(text) > 500 else text)
            return decompressed
        except:
            print("ZLIB解压后不是UTF-8文本")
    except Exception as e:
        print(f"ZLIB解压失败: {e}")

    return None

def main():
    filename = "抖音定时（蓝天版）.txt"

    print("=== 解密分析开始 ===")

    # 读取文件
    content = read_encrypted_file(filename)
    if not content:
        return

    print("\n=== 字符串格式分析 ===")
    analyze_string_format(content)

    print("\n=== 模式分析 ===")
    analyze_pattern(content)

    print("\n=== Base64解码尝试 ===")
    decoded_base64 = try_base64_decode(content)

    print("\n=== URL解码尝试 ===")
    decoded_url = try_url_decode(content)

    # 如果Base64解码成功，进一步分析二进制数据
    if decoded_base64 and isinstance(decoded_base64, bytes):
        print("\n=== 二进制数据分析 ===")
        analyze_binary_data(decoded_base64)

        # 保存二进制数据到文件
        with open("decoded_binary.bin", "wb") as f:
            f.write(decoded_base64)
        print("二进制数据已保存到 decoded_binary.bin")

    # 如果Base64解码成功，尝试进一步分析
    if decoded_base64 and isinstance(decoded_base64, str):
        print("\n=== 对Base64解码结果进行进一步分析 ===")

        # 尝试JSON解析
        try:
            json_data = json.loads(decoded_base64)
            print("成功解析为JSON:")
            print(json.dumps(json_data, indent=2, ensure_ascii=False)[:500])
        except:
            print("不是有效的JSON格式")

        # 查找特定模式
        if "click" in decoded_base64.lower() or "点击" in decoded_base64:
            print("发现点击相关内容!")

        if "douyin" in decoded_base64.lower() or "抖音" in decoded_base64:
            print("发现抖音相关内容!")

if __name__ == "__main__":
    main()
