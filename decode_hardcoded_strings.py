#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解码硬编码字符串
从sub_C7604函数中提取的Base64字符串进行解码分析
"""

import base64
import binascii

def decode_base64_string(encoded_str, description=""):
    """解码Base64字符串"""
    print(f"\n=== {description} ===")
    print(f"原始字符串长度: {len(encoded_str)}")
    print(f"原始字符串: {encoded_str[:100]}...")

    # 尝试多种Base64解码方法
    decode_methods = [
        ("标准Base64", lambda s: base64.b64decode(s)),
        ("URL安全Base64", lambda s: base64.urlsafe_b64decode(s)),
        ("标准Base64+填充", lambda s: base64.b64decode(s + '=' * (4 - len(s) % 4))),
        ("URL安全Base64+填充", lambda s: base64.urlsafe_b64decode(s + '=' * (4 - len(s) % 4))),
    ]

    for method_name, decode_func in decode_methods:
        try:
            decoded = decode_func(encoded_str)
            print(f"{method_name}解码成功，长度: {len(decoded)}")

            # 尝试作为UTF-8文本
            try:
                text = decoded.decode('utf-8')
                print("UTF-8文本:")
                print(text)
                return text
            except UnicodeDecodeError:
                print("不是有效的UTF-8文本")

            # 显示十六进制
            print(f"十六进制: {decoded[:50].hex()}...")

            # 检查是否包含可打印字符
            printable_count = sum(1 for b in decoded if 32 <= b <= 126)
            print(f"可打印字符比例: {printable_count/len(decoded)*100:.2f}%")

            # 尝试查找字符串
            strings = []
            current_string = ""
            for byte in decoded:
                if 32 <= byte <= 126:  # 可打印ASCII字符
                    current_string += chr(byte)
                else:
                    if len(current_string) >= 3:
                        strings.append(current_string)
                    current_string = ""

            if current_string and len(current_string) >= 3:
                strings.append(current_string)

            if strings:
                print("发现的字符串:")
                for s in strings[:10]:  # 只显示前10个
                    print(f"  {s}")

            return decoded

        except Exception as e:
            print(f"{method_name}解码失败: {e}")
            continue

    print("所有解码方法都失败了")
    return None

def main():
    # 从sub_C7604函数中提取的Base64字符串
    strings_to_decode = [
        # 第657-658行的字符串
        ("主要解密字符串", 
         "93bcRyt4K-RqujNG-oOp7owvp3fEAwM3ynsMeO2sY0RTlLRZAaZHpUcRB5mk21Zn1BL2AcYtRhRcxamU4Lc9gxuzqwhL9SVeUCigAIgbwZq_PmqA6bcfYf8I5AzN3HXNtj6QMtOtcmPTU8e2N-ppdGa6P1XYhkVGSHwed0R0lkC7GoJg2n27uIHHA_9V0qHolBPhgVEtSMxBWOdxJdzRdE5Ni00SxC6o"),
        
        # 第714行的字符串
        ("条件分支字符串1",
         "ca53r72r1VkvUAgT-M5hqFdt_5yn3vh7pGFteO1QVwr6tH9fKtmNooB9oTuTvv7xrg4H3qRpdcPL0YhhEZA"),
        
        # 第720-722行的字符串（case -5）
        ("条件分支字符串2 (case -5)",
         "fc06Ud-2BZiej9EXs-qN34uGXYQbXrKsIJli0psK9CZ--r-_KtaTOBqe3OUijzID0OSAeEvst8anJg5SB52cejEYSORmiDHnZ2AnPTN7v8FrYVdH5bLazmUC2Sncms_SAZkU_2QFnl3JWNpxkNfM4A"),
        
        # 第725-729行的字符串（case -4）
        ("条件分支字符串3 (case -4)",
         "59dbRs7sf8-AJUia1uF2xNaqbw9rBkmErddEUcb9Y-ldDup8JCPX_8rpBkq2eHIjaJzlBgTuAJ7FaypW9zvGSNTFmnJs124fCyFASJ2EmjwRTHWi7mTBPpAs9Tjo300dkX33HkaOI5IM4zbGciYoQlMGv2DH5QAK4CbTF8j-S3hvh-XmKIQ-4iNJMwx65pHfKsBsnu1gvP1RRKyFhmeMXg"),
        
        # 第732-736行的字符串（case -3）
        ("条件分支字符串4 (case -3)",
         "eb6dlVYQMOYK60MlOQxwdthamPn1unh5DmQUmyPgcMQhSoji6dbD9voCnU0cVw4aATidBgPFVzB9sDUV96IPDPd17zSlA7XA5MyFmnryfw_K-YOHrRzXjKD4JPbqn45jLZrrbYvcSa72LZZTxl5vZQ"),
        
        # 第767-773行的长字符串
        ("长解密字符串",
         "e40bjWQjoAF5Be0LKNg_O2qHobSxUld2EV2ATps3k_PAQF3sbWI-kIyEeA1g_PSxHq8VKP4r9YWMAdNS0h2vv3gKWGWZfQ9bmUdOvh0H21iRrnnWkWZBe1e6AwLQ2MrvZ8cuTAj2-ChJKnKi8dAfMYyfroPNKHoBV7B9an9AmupEAT5LkRgKpFmBn8yUtFKPF-bCn-jDwztFig8YFFNIAb5DC0A3Q-nMU4FW11x6zj__ivaYEKblTt12FXRAhOh2qfeIk3bPQwOmg11lTDffr_BiYIwus7qJIUjxsa_Y1waSlHRiiQ045dHhcqKPsePN4ExtmkR9H6uVOEtT7MqLcXZOxBb3OjoVfVeQpr5AdIAqV5KvSzdA3ZLXVVz-1Z9ArbH7elyc_w")
    ]
    
    print("=== 解码硬编码Base64字符串 ===")
    
    results = []
    for description, encoded_str in strings_to_decode:
        result = decode_base64_string(encoded_str, description)
        if result:
            results.append((description, result))
    
    # 保存解码结果
    print("\n=== 保存解码结果 ===")
    for i, (description, result) in enumerate(results):
        filename = f"decoded_string_{i+1}_{description.replace(' ', '_').replace('(', '').replace(')', '')}.txt"
        try:
            if isinstance(result, str):
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(result)
            else:
                with open(filename, 'wb') as f:
                    f.write(result)
            print(f"已保存: {filename}")
        except Exception as e:
            print(f"保存失败 {filename}: {e}")

if __name__ == "__main__":
    main()
